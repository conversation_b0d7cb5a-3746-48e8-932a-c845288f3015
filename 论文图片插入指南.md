# 论文图片插入指南

## 图片文件清单

以下是为论文生成的所有图片文件，请按照论文中标注的位置依次插入：

### 1. 图片1_数据集样本展示.png
- **插入位置：** 第二章 2.1 数据分析
- **图片说明：** 展示Twitter数据集的样本数据，包括数据结构和内容示例
- **图注：** 图2-1 数据集样本展示

### 2. 图片2_文本预处理对比.png  
- **插入位置：** 第二章 2.2 分词与清洗流程
- **图片说明：** 对比文本预处理前后的效果，展示清洗流程的作用
- **图注：** 图2-2 文本预处理前后对比

### 3. 图片3_TFIDF特征分布.png
- **插入位置：** 第二章 2.3 词向量化方法  
- **图片说明：** 展示TF-IDF特征的分布情况和统计特性
- **图注：** 图2-3 TF-IDF特征分布分析

### 4. 图片4_整体词云.png
- **插入位置：** 第二章 2.4 词云可视化
- **图片说明：** 整个数据集的词云图，显示高频词汇
- **图注：** 图2-4 整体数据词云分析

### 5. 图片6_最优聚类数分析.png
- **插入位置：** 第三章 3.2 模型构建
- **图片说明：** 肘部法则和轮廓系数分析，确定最优聚类数
- **图注：** 图3-1 最优聚类数分析

### 6. 图片7_聚类结果对比.png
- **插入位置：** 第四章 4.1 模型训练结果
- **图片说明：** 三种聚类算法的可视化结果对比
- **图注：** 图4-1 聚类结果可视化对比

### 7. 图片8_聚类关键词分析.png
- **插入位置：** 第四章 4.2 关键指标分析
- **图片说明：** 各聚类的关键词分析，展示话题特征
- **图注：** 图4-2 各聚类关键词分析

### 8. 图片5_各聚类词云.png
- **插入位置：** 第四章 4.2 关键指标分析（在图4-2之后）
- **图片说明：** 各个聚类的词云对比，直观展示话题差异
- **图注：** 图4-3 各聚类词云对比

### 9. 图片9_算法性能对比.png
- **插入位置：** 第四章 4.2 关键指标分析（最后）
- **图片说明：** 三种算法的性能指标全面对比
- **图注：** 图4-4 算法性能全面对比

## 插入方法

### 在Markdown中插入
```markdown
![图注](图片文件路径)
```

### 在Word中插入
1. 将光标定位到标注的插入位置
2. 选择"插入" → "图片" → "来自文件"
3. 选择对应的图片文件
4. 调整图片大小和位置
5. 添加图注

### 图片格式要求
- 所有图片均为PNG格式，分辨率300 DPI
- 图片尺寸适中，适合论文排版
- 图片清晰，文字可读

## 图片说明

### 技术特点
- 所有图片都包含中英文标题
- 使用了专业的配色方案
- 图表清晰易读，符合学术规范

### 内容覆盖
- 数据展示：原始数据样本和统计信息
- 预处理：文本清洗前后对比
- 特征工程：TF-IDF分布和词云分析  
- 模型分析：聚类数选择和算法对比
- 结果评估：性能指标和话题分析

## 注意事项

1. **图片顺序：** 请严格按照论文中标注的顺序插入图片
2. **图注格式：** 保持图注格式的一致性
3. **图片质量：** 确保插入后图片清晰可读
4. **版面布局：** 注意图片与文字的搭配，保持版面美观

## 额外生成的图片

除了论文中使用的9张主要图片外，还生成了以下辅助图片：
- clustering_results_comparison.png：聚类结果对比（备用）
- optimal_clusters_analysis.png：最优聚类分析（备用）
- overall_wordcloud.png：整体词云（备用）
- wordcloud_cluster_0.png 到 wordcloud_cluster_4.png：各聚类词云（单独文件）

这些图片可以根据需要选择使用或作为补充材料。

## 完成检查

插入所有图片后，请检查：
- [ ] 所有9张图片都已正确插入
- [ ] 图注格式统一且正确
- [ ] 图片清晰可读
- [ ] 图片位置与论文内容匹配
- [ ] 版面布局美观整齐

完成图片插入后，您的论文就可以提交了！
