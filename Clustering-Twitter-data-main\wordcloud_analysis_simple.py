# 简化版词云分析（不依赖wordcloud库）
# Simplified word cloud analysis without wordcloud dependency

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.feature_extraction.text import TfidfVectorizer, ENGLISH_STOP_WORDS
from sklearn.cluster import KMeans
import re
import warnings
from collections import Counter
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def preprocess_text(text):
    """文本预处理函数"""
    if pd.isna(text):
        return ""
    
    # 移除URL链接
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
    # 移除@用户名和#标签
    text = re.sub(r'@\w+|#\w+', '', text)
    # 移除标点符号，保留字母、数字和空格
    text = re.sub(r'[^\w\s]', '', text)
    # 转换为小写
    text = text.lower()
    # 移除停用词
    text = ' '.join([word for word in text.split() if word not in ENGLISH_STOP_WORDS and len(word) > 2])
    return text

def create_word_frequency_chart(texts, title, save_path=None):
    """创建词频图表（替代词云）"""
    # 合并所有文本
    all_text = ' '.join(texts)
    
    # 统计词频
    words = all_text.split()
    word_freq = Counter(words)
    
    # 获取前20个高频词
    top_words = word_freq.most_common(20)
    
    if not top_words:
        print(f"警告：{title} 没有有效词汇")
        return
    
    words, frequencies = zip(*top_words)
    
    # 创建水平条形图
    plt.figure(figsize=(12, 8))
    y_pos = np.arange(len(words))
    
    bars = plt.barh(y_pos, frequencies, color='skyblue', alpha=0.8)
    plt.yticks(y_pos, words)
    plt.xlabel('词频 (Word Frequency)')
    plt.title(f'{title}\n词频分析 (Top 20 Words)', fontsize=14, fontweight='bold')
    plt.gca().invert_yaxis()  # 最高频的词在顶部
    
    # 添加数值标签
    for i, (bar, freq) in enumerate(zip(bars, frequencies)):
        plt.text(bar.get_width() + max(frequencies) * 0.01, bar.get_y() + bar.get_height()/2,
                str(freq), ha='left', va='center', fontweight='bold')
    
    plt.grid(True, alpha=0.3, axis='x')
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()

def main():
    """主函数"""
    print("开始词频分析...")
    
    # 加载数据
    try:
        columns = ['target', 'id', 'date', 'flag', 'user', 'text']
        data = pd.read_csv("twitter.csv", encoding='latin-1', header=None, names=columns)
        print(f"数据加载成功！数据形状: {data.shape}")
    except FileNotFoundError:
        print("错误：未找到twitter.csv文件")
        return
    
    # 文本预处理
    print("进行文本预处理...")
    text_data = data['text']
    text_data_cleaned = text_data.apply(preprocess_text)
    text_data_cleaned = text_data_cleaned[text_data_cleaned.str.len() > 0]
    
    # 采样数据
    sampled_data = text_data_cleaned.sample(n=min(5000, len(text_data_cleaned)), random_state=42)
    print(f"采样数据量: {len(sampled_data)}")
    
    # 生成整体词频图
    print("生成整体词频分析...")
    create_word_frequency_chart(sampled_data.values, 
                               'Twitter数据整体词频分析', 
                               'overall_word_frequency.png')
    
    # TF-IDF向量化和聚类
    print("进行聚类分析...")
    vectorizer = TfidfVectorizer(max_features=500, min_df=2, max_df=0.95)
    text_vectors = vectorizer.fit_transform(sampled_data)
    
    # K-Means聚类
    optimal_k = 5  # 使用固定的聚类数
    kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
    labels = kmeans.fit_predict(text_vectors)
    
    print(f"聚类完成，共{optimal_k}个聚类")
    
    # 为每个聚类生成词频图
    print("生成各聚类词频分析...")
    cluster_names = ['聚类0', '聚类1', '聚类2', '聚类3', '聚类4']
    
    # 创建子图显示所有聚类的词频
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    axes = axes.flatten()
    
    for cluster_id in range(optimal_k):
        cluster_texts = sampled_data[labels == cluster_id]
        if len(cluster_texts) > 0:
            # 合并聚类文本
            all_text = ' '.join(cluster_texts.values)
            words = all_text.split()
            word_freq = Counter(words)
            top_words = word_freq.most_common(10)
            
            if top_words:
                words, frequencies = zip(*top_words)
                
                # 在子图中绘制
                ax = axes[cluster_id]
                y_pos = np.arange(len(words))
                bars = ax.barh(y_pos, frequencies, color=plt.cm.Set3(cluster_id), alpha=0.8)
                ax.set_yticks(y_pos)
                ax.set_yticklabels(words)
                ax.invert_yaxis()
                ax.set_xlabel('词频')
                ax.set_title(f'{cluster_names[cluster_id]}\n({len(cluster_texts)}条推文)', 
                           fontsize=12, fontweight='bold')
                ax.grid(True, alpha=0.3, axis='x')
                
                # 添加数值标签
                for bar, freq in zip(bars, frequencies):
                    ax.text(bar.get_width() + max(frequencies) * 0.02, 
                           bar.get_y() + bar.get_height()/2,
                           str(freq), ha='left', va='center', fontsize=9)
    
    # 隐藏多余的子图
    for i in range(optimal_k, len(axes)):
        axes[i].axis('off')
    
    plt.suptitle('各聚类词频分析对比\nWord Frequency Analysis for Each Cluster', 
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('cluster_word_frequency_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 生成聚类主题分析
    print("生成聚类主题分析...")
    
    # 分析每个聚类的特征词汇
    feature_names = vectorizer.get_feature_names_out()
    
    plt.figure(figsize=(16, 10))
    
    for cluster_id in range(optimal_k):
        cluster_texts = sampled_data[labels == cluster_id]
        
        if len(cluster_texts) > 5:  # 确保有足够的文本
            # 重新计算该聚类的TF-IDF
            cluster_tfidf = vectorizer.transform(cluster_texts)
            
            # 计算平均TF-IDF分数
            mean_scores = np.mean(cluster_tfidf.toarray(), axis=0)
            
            # 获取top关键词
            top_indices = mean_scores.argsort()[-10:][::-1]
            top_words = [feature_names[i] for i in top_indices]
            top_scores = mean_scores[top_indices]
            
            # 创建子图
            plt.subplot(2, 3, cluster_id + 1)
            y_pos = np.arange(len(top_words))
            plt.barh(y_pos, top_scores, color=plt.cm.Set3(cluster_id), alpha=0.8)
            plt.yticks(y_pos, top_words)
            plt.gca().invert_yaxis()
            plt.xlabel('平均TF-IDF值')
            plt.title(f'{cluster_names[cluster_id]}\nTF-IDF关键词', fontsize=12, fontweight='bold')
            plt.grid(True, alpha=0.3, axis='x')
    
    plt.suptitle('各聚类TF-IDF关键词分析\nTF-IDF Keyword Analysis for Each Cluster', 
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('cluster_tfidf_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n词频分析完成！")
    print("生成的文件:")
    print("  - overall_word_frequency.png: 整体词频分析图")
    print("  - cluster_word_frequency_analysis.png: 各聚类词频对比图")
    print("  - cluster_tfidf_analysis.png: 各聚类TF-IDF关键词分析图")

if __name__ == "__main__":
    main()
